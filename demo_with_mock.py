#!/usr/bin/env python3
"""
LemonFox 语音转文字 API Demo（包含模拟响应）
当 API key 无效时，会显示模拟的转录结果
"""

import requests
import json
import tempfile
import os
from typing import Optional, Dict, Any


class LemonFoxSTT:
    """LemonFox 语音转文字 API 客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.lemonfox.ai/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {'Authorization': f'Bearer {api_key}'}
    
    def _get_mock_response(self, response_format: str = "json") -> Dict[Any, Any]:
        """返回模拟的转录响应"""
        mock_text = "这是一段模拟的转录文本。LemonFox API 支持多种语言的语音转文字功能，包括中文、英文等100多种语言。"
        
        if response_format == "json":
            return {"text": mock_text}
        elif response_format == "verbose_json":
            return {
                "text": mock_text,
                "language": "chinese",
                "duration": 15.5,
                "segments": [
                    {
                        "id": 0,
                        "start": 0.0,
                        "end": 7.8,
                        "text": "这是一段模拟的转录文本。",
                        "speaker": "SPEAKER_00"
                    },
                    {
                        "id": 1,
                        "start": 7.8,
                        "end": 15.5,
                        "text": "LemonFox API 支持多种语言的语音转文字功能，包括中文、英文等100多种语言。",
                        "speaker": "SPEAKER_00"
                    }
                ]
            }
        elif response_format == "srt":
            return """1
00:00:00,000 --> 00:00:07,800
这是一段模拟的转录文本。

2
00:00:07,800 --> 00:00:15,500
LemonFox API 支持多种语言的语音转文字功能，包括中文、英文等100多种语言。
"""
        else:
            return mock_text
    
    def transcribe_from_url(
        self,
        audio_url: str,
        language: Optional[str] = None,
        response_format: str = "json",
        speaker_labels: bool = False,
        translate: bool = False,
        prompt: Optional[str] = None
    ) -> Dict[Any, Any]:
        """从 URL 转录音频"""
        
        url = f"{self.base_url}/audio/transcriptions"
        
        data = {
            'file': audio_url,
            'response_format': response_format
        }
        
        if language:
            data['language'] = language
        if speaker_labels:
            data['speaker_labels'] = 'true'
            if response_format == 'json':
                data['response_format'] = 'verbose_json'
        if translate:
            data['translate'] = 'true'
        if prompt:
            data['prompt'] = prompt
        
        try:
            print(f"正在转录音频: {audio_url}")
            print(f"使用参数: {data}")
            
            response = requests.post(url, headers=self.headers, data=data)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"API 请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                if e.response.status_code == 401:
                    print("API key 无效或额度不足，使用模拟响应...")
                    return self._get_mock_response(response_format)
                print(f"响应内容: {e.response.text}")
            raise
    
    def transcribe_from_file(
        self,
        file_path: str,
        language: Optional[str] = None,
        response_format: str = "json",
        speaker_labels: bool = False,
        translate: bool = False,
        prompt: Optional[str] = None
    ) -> Dict[Any, Any]:
        """从本地文件转录音频"""
        
        url = f"{self.base_url}/audio/transcriptions"
        
        data = {'response_format': response_format}
        
        if language:
            data['language'] = language
        if speaker_labels:
            data['speaker_labels'] = 'true'
            if response_format == 'json':
                data['response_format'] = 'verbose_json'
        if translate:
            data['translate'] = 'true'
        if prompt:
            data['prompt'] = prompt
        
        try:
            print(f"正在转录本地文件: {file_path}")
            print(f"使用参数: {data}")
            
            with open(file_path, 'rb') as audio_file:
                files = {'file': audio_file}
                response = requests.post(url, headers=self.headers, data=data, files=files)
                response.raise_for_status()
            
            return response.json()
            
        except FileNotFoundError:
            print(f"文件未找到: {file_path}")
            raise
        except requests.exceptions.RequestException as e:
            print(f"API 请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                if e.response.status_code == 401:
                    print("API key 无效或额度不足，使用模拟响应...")
                    return self._get_mock_response(response_format)
                print(f"响应内容: {e.response.text}")
            raise


def main():
    """主函数 - 演示如何使用 LemonFox API"""
    
    # API 配置
    API_KEY = "OZPWBjuofkESYfgj1cl3KVoFK3rB0hWz"
    AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    
    # 创建客户端
    client = LemonFoxSTT(API_KEY)
    
    print("=== LemonFox 语音转文字 API Demo ===\n")
    print("注意：当前 API key 可能无效，将显示模拟响应\n")
    
    try:
        # 示例 1: 基础转录
        print("1. 基础转录（自动检测语言）")
        result1 = client.transcribe_from_url(AUDIO_URL)
        print(f"转录结果: {result1.get('text', '无文本')}\n")
        
        # 示例 2: 指定语言和详细格式
        print("2. 详细转录（指定中文，verbose_json 格式）")
        result2 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="verbose_json"
        )
        print(f"转录文本: {result2.get('text', '无文本')}")
        if 'segments' in result2:
            print("时间段信息:")
            for i, segment in enumerate(result2['segments'][:3]):
                print(f"  段落 {i+1}: {segment.get('start', 0):.2f}s - {segment.get('end', 0):.2f}s")
                print(f"    文本: {segment.get('text', '')}")
        print()
        
        # 示例 3: 启用说话人识别
        print("3. 说话人识别转录")
        result3 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="verbose_json",
            speaker_labels=True
        )
        print(f"转录文本: {result3.get('text', '无文本')}")
        if 'segments' in result3:
            print("说话人信息:")
            for i, segment in enumerate(result3['segments'][:3]):
                speaker = segment.get('speaker', '未知')
                print(f"  说话人 {speaker}: {segment.get('text', '')}")
        print()
        
        # 示例 4: SRT 字幕格式
        print("4. 生成 SRT 字幕格式")
        result4 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="srt"
        )
        print("SRT 字幕内容:")
        print(result4)
        
    except Exception as e:
        print(f"转录过程中发生错误: {e}")
    
    print("\n=== 使用说明 ===")
    print("1. 获取有效的 API key: https://lemonfox.ai/apis/keys")
    print("2. 确保账户有足够的额度或激活免费试用")
    print("3. 替换代码中的 API_KEY 变量")
    print("4. 支持的音频格式: mp3, wav, flac, aac, opus, ogg, m4a, mp4, mpeg, mov, webm")
    print("5. 文件大小限制: 上传 100MB, URL 方式 1GB")
    print("6. 价格: 每 3 小时音频 $0.50，首月免费")


if __name__ == "__main__":
    main()
