#!/usr/bin/env python3
"""
LemonFox Speech-to-Text API Demo
使用 LemonFox API 将音频转换为文字的示例代码
"""

import requests
import json
import time
from typing import Optional, Dict, Any


class LemonFoxSTT:
    """LemonFox 语音转文字 API 客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.lemonfox.ai/v1"):
        """
        初始化客户端
        
        Args:
            api_key: LemonFox API 密钥
            base_url: API 基础 URL，默认为全球服务器，可选择 EU 服务器
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}'
        }
    
    def transcribe_from_url(
        self,
        audio_url: str,
        language: Optional[str] = None,
        response_format: str = "json",
        speaker_labels: bool = False,
        translate: bool = False,
        prompt: Optional[str] = None
    ) -> Dict[Any, Any]:
        """
        从 URL 转录音频
        
        Args:
            audio_url: 音频文件的公开 URL
            language: 音频语言（可选，不提供则自动检测）
            response_format: 响应格式 (json, text, srt, verbose_json, vtt)
            speaker_labels: 是否启用说话人识别
            translate: 是否翻译为英文
            prompt: 提示文本，用于指导转录风格
            
        Returns:
            API 响应结果
        """
        url = f"{self.base_url}/audio/transcriptions"
        
        # 准备表单数据
        data = {
            'file': audio_url,
            'response_format': response_format
        }
        
        # 添加可选参数
        if language:
            data['language'] = language
        if speaker_labels:
            data['speaker_labels'] = 'true'
            # 如果启用说话人识别，建议使用 verbose_json 格式
            if response_format == 'json':
                data['response_format'] = 'verbose_json'
                print("提示：启用说话人识别时建议使用 verbose_json 格式")
        if translate:
            data['translate'] = 'true'
        if prompt:
            data['prompt'] = prompt
        
        try:
            print(f"正在转录音频: {audio_url}")
            print(f"使用参数: {data}")
            
            response = requests.post(url, headers=self.headers, data=data)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"API 请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            raise
    
    def transcribe_from_file(
        self,
        file_path: str,
        language: Optional[str] = None,
        response_format: str = "json",
        speaker_labels: bool = False,
        translate: bool = False,
        prompt: Optional[str] = None
    ) -> Dict[Any, Any]:
        """
        从本地文件转录音频
        
        Args:
            file_path: 本地音频文件路径
            language: 音频语言（可选，不提供则自动检测）
            response_format: 响应格式 (json, text, srt, verbose_json, vtt)
            speaker_labels: 是否启用说话人识别
            translate: 是否翻译为英文
            prompt: 提示文本，用于指导转录风格
            
        Returns:
            API 响应结果
        """
        url = f"{self.base_url}/audio/transcriptions"
        
        # 准备表单数据
        data = {
            'response_format': response_format
        }
        
        # 添加可选参数
        if language:
            data['language'] = language
        if speaker_labels:
            data['speaker_labels'] = 'true'
            if response_format == 'json':
                data['response_format'] = 'verbose_json'
                print("提示：启用说话人识别时建议使用 verbose_json 格式")
        if translate:
            data['translate'] = 'true'
        if prompt:
            data['prompt'] = prompt
        
        try:
            print(f"正在转录本地文件: {file_path}")
            print(f"使用参数: {data}")
            
            with open(file_path, 'rb') as audio_file:
                files = {'file': audio_file}
                response = requests.post(url, headers=self.headers, data=data, files=files)
                response.raise_for_status()
            
            return response.json()
            
        except FileNotFoundError:
            print(f"文件未找到: {file_path}")
            raise
        except requests.exceptions.RequestException as e:
            print(f"API 请求失败: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            raise


def main():
    """主函数 - 演示如何使用 LemonFox API"""
    
    # API 配置
    API_KEY = "OZPWBjuofkESYfgj1cl3KVoFK3rB0hWz"
    AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"
    
    # 创建客户端
    client = LemonFoxSTT(API_KEY)
    
    print("=== LemonFox 语音转文字 API Demo ===\n")
    
    try:
        # 示例 1: 基础转录
        print("1. 基础转录（自动检测语言）")
        result1 = client.transcribe_from_url(AUDIO_URL)
        print(f"转录结果: {result1.get('text', '无文本')}\n")
        
        # 示例 2: 指定语言和详细格式
        print("2. 详细转录（指定中文，verbose_json 格式）")
        result2 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="verbose_json"
        )
        print(f"转录文本: {result2.get('text', '无文本')}")
        if 'segments' in result2:
            print("时间段信息:")
            for i, segment in enumerate(result2['segments'][:3]):  # 只显示前3段
                print(f"  段落 {i+1}: {segment.get('start', 0):.2f}s - {segment.get('end', 0):.2f}s")
                print(f"    文本: {segment.get('text', '')}")
        print()
        
        # 示例 3: 启用说话人识别
        print("3. 说话人识别转录")
        result3 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="verbose_json",
            speaker_labels=True
        )
        print(f"转录文本: {result3.get('text', '无文本')}")
        if 'segments' in result3:
            print("说话人信息:")
            for i, segment in enumerate(result3['segments'][:3]):  # 只显示前3段
                speaker = segment.get('speaker', '未知')
                print(f"  说话人 {speaker}: {segment.get('text', '')}")
        print()
        
        # 示例 4: SRT 字幕格式
        print("4. 生成 SRT 字幕格式")
        result4 = client.transcribe_from_url(
            AUDIO_URL,
            language="chinese",
            response_format="srt"
        )
        print("SRT 字幕内容:")
        print(result4)
        
    except Exception as e:
        print(f"转录过程中发生错误: {e}")


if __name__ == "__main__":
    main()
