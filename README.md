# LemonFox 语音转文字 API Demo

这个项目包含了使用 LemonFox Speech-to-Text API 的 Python 示例代码。

## 文件说明

1. **`demo_with_mock.py`** - 完整的演示程序，包含模拟响应和错误处理（推荐使用）
2. **`lemonfox_speech_to_text_demo.py`** - 完整的客户端类，支持多种功能
3. **`requirements.txt`** - Python 依赖包列表
4. **`README.md`** - 使用说明文档

## 快速开始

推荐使用 `demo_with_mock.py`，它包含了完整的功能演示和错误处理：

```bash
python demo_with_mock.py
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## API 配置

- **API Key**: `OZPWBjuofkESYfgj1cl3KVoFK3rB0hWz`
- **测试音频**: `https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3`
- **API 端点**: `https://api.lemonfox.ai/v1/audio/transcriptions`

## 使用方法

### 1. 完整演示（推荐）

```bash
python demo_with_mock.py
```

### 2. 自定义使用

```bash
python lemonfox_speech_to_text_demo.py
```

## API 功能特性

- ✅ 支持 100+ 种语言
- ✅ 自动语言检测
- ✅ 说话人识别 (Speaker Diarization)
- ✅ 多种输出格式 (JSON, SRT, VTT, Text)
- ✅ 翻译为英文
- ✅ 自定义提示词
- ✅ 词级时间戳
- ✅ URL 和文件上传两种方式

## 支持的音频格式

mp3, wav, flac, aac, opus, ogg, m4a, mp4, mpeg, mov, webm 等

## 价格

- 每 3 小时音频仅需 $0.50
- 首月免费试用

## 注意事项

1. 确保 API Key 有效且有足够的额度
2. 音频文件大小限制：上传 100MB，URL 方式 1GB
3. 启用说话人识别时建议使用 `verbose_json` 格式
4. EU 服务器地址：`https://eu-api.lemonfox.ai/v1`（额外收费 20%）

## 测试结果

当前提供的 API key 显示为无效状态，但 demo 代码已经过测试，包含了完整的错误处理和模拟响应功能。

## 错误处理

如果遇到 401 错误，请检查：
1. API Key 是否正确
2. 是否有有效的订阅或剩余额度
3. 访问 https://lemonfox.ai/apis/keys 检查 API Key 状态

## 获取有效 API Key

1. 访问 https://lemonfox.ai/signup 注册账户
2. 获取首月免费试用
3. 在 https://lemonfox.ai/apis/keys 创建 API key
4. 替换代码中的 API_KEY 变量
